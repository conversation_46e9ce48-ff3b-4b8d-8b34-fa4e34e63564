#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预留开孔装置表处理模块
主要功能：从设计说明书中提取预留开孔装置数据并生成Excel表格
"""

import pandas as pd
import os
from datetime import datetime
import logging

# 导入日志配置
from logger_config import get_design_doc_logger, log_function_start, log_function_end, log_function_error, log_process_step, log_data_info

# 获取日志记录器
logger = get_design_doc_logger()
logger.setLevel(logging.WARNING)  # 只显示WARNING及以上日志


class ReservedOpeningProcessor:
    """预留开孔装置表处理器"""

    def __init__(self):
        self.devices_data = []
        self.grouped_devices_data = []
        self.tape_usage_data = []
        self.output_file_path = None
        self.error_messages = []
    
    def process_reserved_opening_data(self, reserved_opening_data, output_dir="output"):
        """
        处理预留开孔装置数据并生成Excel表格
        
        Args:
            reserved_opening_data (dict): 预留开孔装置数据
            output_dir (str): 输出目录
            
        Returns:
            tuple: (success, output_file_path, error_msg)
        """
        try:
            log_function_start(logger, "process_reserved_opening_data")
            
            if not reserved_opening_data or not reserved_opening_data.get('devices'):
                return False, None, "没有预留开孔装置数据"
            
            # 提取设备数据
            devices = reserved_opening_data['devices']
            log_data_info(logger, "预留开孔装置数据", details=f"共{len(devices)}台设备")
            
            # 转换为DataFrame格式（原始数据）
            df_data = []
            for device in devices:
                df_data.append({
                    '屏柜编号': device['cabinet_no'],
                    '设备名称': device['device_name'],
                    '数量': int(device['quantity'])
                })

            # 创建原始DataFrame
            df_original = pd.DataFrame(df_data)

            # 按屏柜编号分组求和
            df_grouped = df_original.groupby('屏柜编号')['数量'].sum().reset_index()
            df_grouped.columns = ['屏柜编号', '设备总数量']

            # 生成胶带用量表
            tape_df = self._generate_tape_usage_table(df_grouped)
            
            # 确保输出目录存在
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 生成输出文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"预留开孔装置表_{timestamp}.xlsx"
            output_file_path = os.path.join(output_dir, output_filename)
            
            # 保存到Excel文件
            with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
                # 保存原始预留开孔装置表
                df_original.to_excel(writer, sheet_name='预留开孔装置表(明细)', index=False)

                # 保存分组汇总表
                df_grouped.to_excel(writer, sheet_name='预留开孔装置表', index=False)

                # 保存胶带用量表
                if tape_df is not None and not tape_df.empty:
                    tape_df.to_excel(writer, sheet_name='胶带用量', index=False)

                # 格式化预留开孔装置表(明细)
                self._format_worksheet(writer, '预留开孔装置表(明细)', df_original,
                                     ['屏柜编号', '设备名称', '数量'], [15, 30, 10])

                # 格式化预留开孔装置表(汇总)
                self._format_worksheet(writer, '预留开孔装置表', df_grouped,
                                     ['屏柜编号', '设备总数量'], [15, 15])

                # 格式化胶带用量表
                if tape_df is not None and not tape_df.empty:
                    self._format_worksheet(writer, '胶带用量', tape_df,
                                         ['物料名称', '物料型号', '星瀚编码', '星空编码', '长度'],
                                         [15, 20, 15, 15, 15])
            
            self.output_file_path = output_file_path
            self.devices_data = df_data
            self.grouped_devices_data = df_grouped.to_dict('records')
            if tape_df is not None:
                self.tape_usage_data = tape_df.to_dict('records')

            log_function_end(logger, "process_reserved_opening_data", f"成功生成预留开孔装置表: {output_file_path}")
            return True, output_file_path, ""
            
        except Exception as e:
            error_msg = f"生成预留开孔装置表时发生错误: {str(e)}"
            log_function_error(logger, "process_reserved_opening_data", e)
            return False, None, error_msg
    
    def get_summary(self):
        """获取处理摘要"""
        if not self.devices_data:
            return "没有处理数据"

        summary = f"预留开孔装置表处理摘要:\n"
        summary += f"  - 明细记录数量: {len(self.devices_data)}\n"

        # 按屏柜编号统计
        if self.grouped_devices_data:
            summary += f"  - 分组汇总记录: {len(self.grouped_devices_data)}个屏柜\n"
            total_devices = sum(item['设备总数量'] for item in self.grouped_devices_data)
            summary += f"  - 设备总数量: {total_devices}台\n"

            for item in self.grouped_devices_data:
                summary += f"    * {item['屏柜编号']}: {item['设备总数量']}台设备\n"

        # 胶带用量统计
        if self.tape_usage_data:
            summary += f"  - 胶带用量记录: {len(self.tape_usage_data)}条\n"
            for tape in self.tape_usage_data:
                summary += f"    * {tape['物料名称']} ({tape['物料型号']}): {tape['长度']}m\n"

        if self.output_file_path:
            summary += f"  - 输出文件: {self.output_file_path}\n"

        return summary

    def _generate_tape_usage_table(self, grouped_df):
        """生成胶带用量表"""
        try:
            log_process_step(logger, "开始生成胶带用量表")

            # 读取物料配置文件
            config_file = r'D:\cursor_workspace\Eplan-导线\input\配置文件\物料辅料与把手.xlsx'

            if not os.path.exists(config_file):
                log_process_step(logger, f"配置文件不存在: {config_file}")
                return None

            # 读取配置文件
            config_df = pd.read_excel(config_file, sheet_name='Sheet1')

            # 查找胶带相关记录
            tape_records = config_df[config_df['备注关键字'].str.contains('预留.*开孔', na=False, regex=True)]

            if tape_records.empty:
                log_process_step(logger, "配置文件中未找到预留开孔相关的胶带记录")
                return None

            # 计算总设备数量
            total_devices = grouped_df['设备总数量'].sum()
            log_data_info(logger, "设备总数量", details=f"{total_devices}")

            # 生成胶带用量表
            tape_usage_data = []

            for _, tape_record in tape_records.iterrows():
                # 计算长度：总设备数量 × 配置文件中的长度
                calculated_length = total_devices * tape_record['长度']

                tape_usage_data.append({
                    '物料名称': tape_record['物料名称'],
                    '物料型号': tape_record['物料型号'],
                    '星瀚编码': tape_record['物料星瀚编码'],
                    '星空编码': tape_record['物料星空编码'] if tape_record['物料星空编码'] != '/' else '',
                    '长度': round(calculated_length, 6)
                })

                log_data_info(logger, "胶带用量计算",
                            details=f"{tape_record['物料名称']}: {total_devices} × {tape_record['长度']} = {calculated_length}")

            tape_df = pd.DataFrame(tape_usage_data)
            log_process_step(logger, f"成功生成胶带用量表，共{len(tape_df)}条记录")

            return tape_df

        except Exception as e:
            log_function_error(logger, "_generate_tape_usage_table", e)
            return None

    def _format_worksheet(self, writer, sheet_name, df, column_names, column_widths):
        """格式化工作表"""
        try:
            from openpyxl.styles import Font, Alignment, PatternFill

            worksheet = writer.sheets[sheet_name]

            # 设置列宽
            for i, width in enumerate(column_widths):
                col_letter = chr(65 + i)  # A, B, C, ...
                worksheet.column_dimensions[col_letter].width = width

            # 设置标题行格式
            title_font = Font(bold=True, size=12)
            title_alignment = Alignment(horizontal='center', vertical='center')
            title_fill = PatternFill(start_color='E6E6FA', end_color='E6E6FA', fill_type='solid')

            for col in range(1, len(column_names) + 1):
                cell = worksheet.cell(row=1, column=col)
                cell.font = title_font
                cell.alignment = title_alignment
                cell.fill = title_fill

            # 设置数据行格式
            data_alignment = Alignment(horizontal='center', vertical='center')
            for row in range(2, len(df) + 2):
                for col in range(1, len(column_names) + 1):
                    cell = worksheet.cell(row=row, column=col)
                    cell.alignment = data_alignment

        except Exception as e:
            log_function_error(logger, "_format_worksheet", e)


def create_reserved_opening_table(design_doc_path, output_dir="output"):
    """
    从设计说明书创建预留开孔装置表的便捷函数
    
    Args:
        design_doc_path (str): 设计说明书文件路径
        output_dir (str): 输出目录
        
    Returns:
        tuple: (success, output_file_path, summary, error_msg)
    """
    try:
        # 导入设计说明书处理器
        from design_doc_processor import DesignDocProcessor
        
        # 处理设计说明书
        processor = DesignDocProcessor()
        result = processor.process_design_document(design_doc_path)
        material_spec, cable_type, is_rotating_cabinet, small_busbar_data, purchase_category_data, reserved_opening_data, success, error_msg = result
        
        if not success:
            return False, None, "", f"处理设计说明书失败: {error_msg}"
        
        if not reserved_opening_data or not reserved_opening_data.get('devices'):
            return False, None, "", "设计说明书中没有预留开孔装置数据"
        
        # 生成预留开孔装置表
        reserved_processor = ReservedOpeningProcessor()
        success, output_file_path, error_msg = reserved_processor.process_reserved_opening_data(reserved_opening_data, output_dir)
        
        if success:
            summary = reserved_processor.get_summary()
            return True, output_file_path, summary, ""
        else:
            return False, None, "", error_msg
            
    except Exception as e:
        return False, None, "", f"创建预留开孔装置表时发生错误: {str(e)}"


if __name__ == "__main__":
    # 测试代码
    test_file = r"D:\cursor_workspace\Eplan-导线\test\浙江嘉兴许村220kV输变电工程智能变电站监控系统\设计说明书[SZ2507107][浙江嘉兴许村220kV输变电工程智能变电站监控系统][20250812]638905865655340408.xls"
    
    print("预留开孔装置表处理模块测试")
    print("=" * 50)
    
    success, output_file, summary, error_msg = create_reserved_opening_table(test_file)
    
    if success:
        print("✓ 处理成功")
        print(f"输出文件: {output_file}")
        print("\n" + summary)
    else:
        print("✗ 处理失败")
        print(f"错误信息: {error_msg}")
    
    print("=" * 50)

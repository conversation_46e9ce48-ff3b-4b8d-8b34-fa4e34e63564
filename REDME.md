1、main.py--运行主程序

2、excel_file_selector.py--GUI程序

3、excel_formatter.py--生成表格列宽行宽自适应

4、parallel_wire_statistics.py--并线表统计

5、wire_code_matcher.py--编码匹配

6、wire_count_processor.py--导线根数统计

7、wire_length_processor.py--导线长度计算

8、preprocess_wiring_table.py--导线数据预处理（包含回车符处理和设备类型智能识别）

9、wire_diameter_matcher.py--导线线径匹配

10、terminal_matching.py--压头匹配

11、sleeve_matching.py--套管匹配

12、sleeve_matching_fujian.py--福建特殊项目套管匹配

13、color_band_processor.py--色带用量计算

14、design_doc_processor.py--GUI数据预处理

15、logger_config.py--日志生成

16、bom_processor.py--BOM清单生成

17、batch_processor.py--批量处理模块

18、short_wire_processor.py--装置短接线处理

19、decimal_calculator.py--使用decimal模块进行小数计算

20、residual_manager.py--残值管理模块

21、multi_core_wire_processor.py--普通多芯线处理模块

22、pe_grounding_processor.py-装置PE接地导线处理模块

23、reserved_opening_processor.py--预留开孔装置表处理模块
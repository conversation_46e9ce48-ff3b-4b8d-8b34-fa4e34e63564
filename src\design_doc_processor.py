#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设计说明书处理模块
主要功能：从设计说明书中自动识别线材规格和线径选型
"""

import pandas as pd
import re
from tkinter import messagebox
import logging

# 导入日志配置
from logger_config import get_design_doc_logger, log_function_start, log_function_end, log_function_error, log_process_step, log_data_info

# 获取设计说明书处理日志记录器
logger = get_design_doc_logger()
logger.setLevel(logging.WARNING)  # 只显示WARNING及以上日志


class DesignDocProcessor:
    """设计说明书处理器"""
    
    def __init__(self):
        self.material_spec = None
        self.cable_type = None
        self.cabinet_type = None  # 新增：柜式要求
        self.is_rotating_cabinet = False  # 新增：是否为旋转柜

        # 新增：小母线支架和小母线铜棒相关属性
        self.small_busbar_bracket = None  # 小母线支架信息
        self.small_busbar_bracket_layout = None  # 小母线支架布局方式
        self.small_busbar_bracket_quantity = None  # 小母线支架数量

        self.small_busbar_copper = None  # 小母线铜棒信息
        self.small_busbar_copper_spec = None  # 小母线铜棒规格
        self.small_busbar_copper_quantity = None  # 小母线铜棒数量

        # 新增：外购类别相关属性
        self.purchase_categories = []  # 外购类别列表
        self.purchase_category_data = None  # 外购类别详细数据

        # 新增：预留开孔装置相关属性
        self.reserved_opening_devices = []  # 预留开孔装置列表
        self.reserved_opening_data = None  # 预留开孔装置详细数据

        self.error_messages = []
    
    def process_design_document(self, design_doc_path):
        """
        处理设计说明书，提取线材规格和线径选型
        
        Args:
            design_doc_path (str): 设计说明书文件路径
            
        Returns:
            tuple: (material_spec, cable_type, is_rotating_cabinet, small_busbar_data, purchase_category_data, reserved_opening_data, success, error_msg)
                  - material_spec: 线材规格
                  - cable_type: 线径选型
                  - is_rotating_cabinet: 是否为旋转柜
                  - small_busbar_data: 小母线数据字典
                  - purchase_category_data: 外购类别数据字典
                  - reserved_opening_data: 预留开孔装置数据字典
                  - success: 是否成功识别
                  - error_msg: 错误信息
        """
        try:
            # 清空之前的结果
            self.material_spec = None
            self.cable_type = None
            self.cabinet_type = None
            self.is_rotating_cabinet = False
            
            # 清空小母线相关数据
            self.small_busbar_bracket = None
            self.small_busbar_bracket_layout = None
            self.small_busbar_bracket_quantity = None
            self.small_busbar_copper = None
            self.small_busbar_copper_spec = None
            self.small_busbar_copper_quantity = None

            # 清空外购类别相关数据
            self.purchase_categories = []
            self.purchase_category_data = None

            # 清空预留开孔装置相关数据
            self.reserved_opening_devices = []
            self.reserved_opening_data = None

            self.error_messages = []
            
            log_function_start(logger, "process_design_document", file=design_doc_path)
            
            # 读取设计说明书Excel文件
            workbook_data = self._read_design_document(design_doc_path)
            if not workbook_data:
                return None, None, False, None, None, None, False, "无法读取设计说明书文件"
            
            # 查找"材料附件选型"工作表
            material_sheet = self._find_material_sheet(workbook_data)
            if material_sheet is None:
                return None, None, False, None, None, None, False, "未找到'材料附件选型'工作表"
            
            # 处理线材规格
            material_result = self._process_material_spec(material_sheet)
            
            # 处理线径选型
            cable_result = self._process_cable_type(material_sheet)
            
            # 处理柜式要求（从"选型要求及组屏方案"工作表读取）
            self._process_cabinet_type(workbook_data)
            
            # 新增：处理小母线支架和小母线铜棒
            self._process_small_busbar_bracket(material_sheet)
            self._process_small_busbar_copper(material_sheet)

            # 新增：处理外购类别（从"选型要求及组屏方案"工作表读取）
            self._process_purchase_category(workbook_data)

            # 新增：处理预留开孔装置（从"选型要求及组屏方案"工作表读取）
            self._process_reserved_opening_devices(workbook_data)
            
            # 组装小母线数据
            small_busbar_data = {
                'bracket': {
                    'layout': self.small_busbar_bracket_layout,
                    'quantity': self.small_busbar_bracket_quantity,
                    'layers': self.small_busbar_bracket.get('layers', 1) if self.small_busbar_bracket else 1,  # 新增层数信息
                    'raw_data': self.small_busbar_bracket
                },
                'copper': {
                    'spec': self.small_busbar_copper_spec,
                    'quantity': self.small_busbar_copper_quantity,
                    'raw_data': self.small_busbar_copper
                }
            }

            # 使用处理后的外购类别数据
            purchase_category_data = self.purchase_category_data

            # 使用处理后的预留开孔装置数据
            reserved_opening_data = self.reserved_opening_data
            
            # 检查处理结果
            if self.error_messages:
                error_msg = "\n".join(self.error_messages)
                log_function_end(logger, "process_design_document", f"部分成功，有错误: {error_msg}")
                return self.material_spec, self.cable_type, self.is_rotating_cabinet, small_busbar_data, purchase_category_data, reserved_opening_data, False, error_msg
            
            log_function_end(logger, "process_design_document", "完全成功")
            log_data_info(logger, "识别结果", details=f"线材规格: {self.material_spec}, 线径选型: {self.cable_type}, 旋转柜: {self.is_rotating_cabinet}")
            if self.small_busbar_bracket_layout:
                log_data_info(logger, "小母线支架", details=f"布局方式: {self.small_busbar_bracket_layout}, 数量: {self.small_busbar_bracket_quantity}")
            if self.small_busbar_copper_spec:
                log_data_info(logger, "小母线铜棒", details=f"规格: {self.small_busbar_copper_spec}, 数量: {self.small_busbar_copper_quantity}")
            if self.purchase_categories:
                log_data_info(logger, "外购类别", details=f"类别数量: {len(self.purchase_categories)}, 类别: {', '.join(set(self.purchase_categories))}")
            if self.reserved_opening_devices:
                log_data_info(logger, "预留开孔装置", details=f"设备数量: {len(self.reserved_opening_devices)}")

            return self.material_spec, self.cable_type, self.is_rotating_cabinet, small_busbar_data, purchase_category_data, reserved_opening_data, True, ""
            
        except Exception as e:
            error_msg = f"处理设计说明书时发生错误: {str(e)}"
            log_function_error(logger, "process_design_document", e)
            return None, None, False, None, None, None, False, error_msg
    
    def _read_design_document(self, design_doc_path):
        """读取设计说明书Excel文件"""
        try:
            # 读取所有工作表，sheet_name=None表示读取所有工作表，header=None表示不使用标题行
            excel_data = pd.read_excel(design_doc_path, sheet_name=None, header=None)  # type: ignore
            logger.info(f"成功读取设计说明书，包含工作表: {list(excel_data.keys())}")
            return excel_data
        except Exception as e:
            logger.error(f"读取设计说明书失败: {e}")
            return None
    
    def _find_material_sheet(self, workbook_data):
        """查找材料附件选型工作表"""
        logger.info(f"查找材料附件选型工作表，可用工作表: {list(workbook_data.keys())}")
        
        # 首先精确匹配"材料附件选型"
        if "材料附件选型" in workbook_data:
            logger.info(f"找到精确匹配的材料附件选型工作表")
            return workbook_data["材料附件选型"]
        
        # 然后尝试部分匹配，但优先级更高的匹配
        priority_names = ["材料附件选型", "材料选型", "附件选型"]
        
        for priority_name in priority_names:
            for sheet_name, sheet_data in workbook_data.items():
                if priority_name in sheet_name:
                    logger.info(f"找到材料附件选型工作表: {sheet_name}")
                    return sheet_data
        
        # 最后尝试更宽泛的匹配，但要排除明显不相关的
        broader_names = ["材料", "选型"]
        for broader_name in broader_names:
            for sheet_name, sheet_data in workbook_data.items():
                # 排除明显不相关的工作表
                if broader_name in sheet_name and not any(exclude in sheet_name for exclude in ["要求", "方案", "配置", "记录"]):
                    logger.info(f"找到材料附件选型工作表: {sheet_name}")
                    return sheet_data
        
        # 如果都没找到，通过内容识别
        for sheet_name, sheet_data in workbook_data.items():
            if self._contains_material_keywords(sheet_data):
                logger.info(f"通过内容识别到材料附件选型工作表: {sheet_name}")
                return sheet_data
        
        logger.warning("未找到材料附件选型工作表")
        return None
    
    def _contains_material_keywords(self, sheet_data):
        """检查工作表是否包含材料相关关键词"""
        keywords = ["线材", "线径", "材料", "规格", "选型"]
        try:
            # 转换为字符串并检查是否包含关键词
            sheet_str = sheet_data.astype(str).values.flatten()
            content = " ".join(sheet_str)
            return sum(1 for keyword in keywords if keyword in content) >= 3
        except:
            return False
    
    def _process_material_spec(self, material_sheet):
        """处理线材规格"""
        try:
            # 在A列查找"线材"
            material_row = self._find_row_by_keyword(material_sheet, "线材")
            if material_row is None:
                self.error_messages.append("在设计说明书中未找到'线材'行")
                return False
            
            # 查找"规格"列并获取右侧数据
            spec_value = self._get_spec_value(material_sheet, material_row, "规格")
            if spec_value:
                self.material_spec = spec_value
                logger.info(f"识别到线材规格: {self.material_spec}")
                return True
            else:
                # 查找备注信息
                remark = self._get_remark_value(material_sheet, material_row, "规格")
                error_msg = f"'线材'行的'规格'数据为空"
                if remark:
                    error_msg += f"，备注信息: {remark}"
                self.error_messages.append(error_msg)
                return False
                
        except Exception as e:
            self.error_messages.append(f"处理线材规格时出错: {str(e)}")
            return False
    
    def _process_cable_type(self, material_sheet):
        """处理线径选型"""
        try:
            # 在A列查找"线径"
            cable_row = self._find_row_by_keyword(material_sheet, "线径")
            if cable_row is None:
                self.error_messages.append("在设计说明书中未找到'线径'行")
                return False
            
            # 查找"选型"列并获取右侧数据
            type_value = self._get_spec_value(material_sheet, cable_row, "选型")
            if type_value:
                # 提取G、N、Z字符
                extracted_type = self._extract_cable_type(type_value)
                if extracted_type:
                    self.cable_type = extracted_type
                    logger.info(f"识别到线径选型: {self.cable_type}")
                    return True
                else:
                    self.error_messages.append(f"'线径'行的'选型'数据'{type_value}'中未找到G、N或Z")
                    return False
            else:
                # 查找备注信息
                remark = self._get_remark_value(material_sheet, cable_row, "选型")
                error_msg = f"'线径'行的'选型'数据为空"
                if remark:
                    error_msg += f"，备注信息: {remark}"
                self.error_messages.append(error_msg)
                return False
                
        except Exception as e:
            self.error_messages.append(f"处理线径选型时出错: {str(e)}")
            return False
    
    def _find_row_by_keyword(self, sheet_data, keyword):
        """在A列查找包含关键词的行"""
        try:
            # 获取A列数据
            a_column = sheet_data.iloc[:, 0].astype(str)
                         # 查找包含关键词的行
            for idx, cell_value in enumerate(a_column):
                cell_str = str(cell_value).strip()
                
                if keyword in cell_str and cell_str != 'nan':
                    # print(f"✓ 找到'{keyword}'在第{idx + 1}行")  # 使用print而不是logger，确保用户能看到
                    return idx
            
            # 如果没找到，静默返回（GUI会处理错误显示）
            return None
        except Exception as e:
            logger.error(f"查找关键词'{keyword}'时出错: {e}")
            return None
    
    def _get_spec_value(self, sheet_data, target_row, spec_keyword):
        """获取规格或选型右侧的值"""
        try:
            # 根据分析结果，数据结构通常是：A列=关键词，B列=属性名，C列=值
            # 例如：线材 | 规格 | 普通型  或  线径 | 选型 | G型(国网)
            
            row_data = sheet_data.iloc[target_row, :].astype(str)
            
            # 方法1：直接获取C列的值（索引2）
            if len(row_data) > 2:
                direct_value = str(row_data.iloc[2]).strip()
                if direct_value and direct_value.lower() not in ['nan', 'none', '']:
                    # print(f"✓ 识别到{spec_keyword}: {direct_value}")  # 使用print确保用户能看到
                    return direct_value
            
            # 方法2：在行中查找规格或选型列，然后获取右侧值
            for col_idx, cell_value in enumerate(row_data):
                cell_str = str(cell_value).strip()
                if spec_keyword in cell_str and cell_str != 'nan':
                    # 获取右侧单元格的值
                    if col_idx + 1 < len(row_data):
                        right_value = str(row_data.iloc[col_idx + 1]).strip()
                        if right_value and right_value.lower() not in ['nan', 'none', '']:
                            # print(f"✓ 识别到{spec_keyword}: {right_value}")
                            return right_value
            
            return None
        except Exception as e:
            logger.error(f"获取{spec_keyword}值时出错: {e}")
            return None
    
    def _get_remark_value(self, sheet_data, target_row, spec_keyword):
        """获取备注信息"""
        try:
            # 查找下一行和下几行的备注
            for offset in range(1, 5):  # 查找接下来的4行
                if target_row + offset < len(sheet_data):
                    check_row_data = sheet_data.iloc[target_row + offset, :].astype(str)
                    
                    # 查找包含"备注"、"注释"、"说明"等关键词的行
                    for col_idx, cell_value in enumerate(check_row_data):
                        cell_str = str(cell_value).strip()
                        if any(keyword in cell_str for keyword in ["备注", "注释", "说明"]):
                            # 获取该单元格的值作为备注
                            if len(cell_str) > 10:  # 如果内容较长，直接返回
                                logger.info(f"找到{spec_keyword}备注信息: {cell_str[:100]}...")
                                return cell_str
                            
                            # 否则查找右侧单元格
                            if col_idx + 1 < len(check_row_data):
                                remark_value = str(check_row_data.iloc[col_idx + 1]).strip()
                                if remark_value and remark_value.lower() not in ['nan', 'none', '']:
                                    logger.info(f"找到{spec_keyword}备注信息: {remark_value}")
                                    return remark_value
                    
                    # 如果整行看起来像注释（包含较多文字），也返回
                    row_content = " ".join([str(cell) for cell in check_row_data if str(cell).strip() not in ['nan', 'none', '']])
                    if len(row_content) > 20 and ("规格" in row_content or "选型" in row_content):
                        logger.info(f"找到{spec_keyword}相关说明: {row_content[:100]}...")
                        return row_content
            
            return None
        except Exception as e:
            logger.error(f"获取备注信息时出错: {e}")
            return None
    
    def _extract_cable_type(self, type_value):
        """从选型数据中提取G、N、Z"""
        try:
            # 首先检查南网常规类型的特殊情况
            nanwang_keywords = ["南网常规A类", "南网常规B类", "南网常规类"]
            for keyword in nanwang_keywords:
                if keyword in type_value:
                    # print(f"✓ 识别到南网类型'{keyword}'，判定为N型")
                    return "N"
            
            # 使用正则表达式查找G、N、Z
            pattern = r'[GNZ]'
            matches = re.findall(pattern, type_value.upper())
            
            if matches:
                # 返回第一个匹配的字符
                return matches[0]
            
            return None
        except Exception as e:
            logger.error(f"提取线径选型字符时出错: {e}")
            return None

    def _process_cabinet_type(self, workbook_data):
        """处理柜式要求信息"""
        try:
            # 查找"选型要求及组屏方案"工作表
            cabinet_sheet = self._find_cabinet_sheet(workbook_data)
            if cabinet_sheet is None:
                logger.warning("未找到'选型要求及组屏方案'工作表，跳过柜式要求处理")
                return
            
            # 在A列查找"柜式要求"
            cabinet_row = self._find_row_by_keyword(cabinet_sheet, "柜式要求")
            if cabinet_row is None:
                logger.warning("在设计说明书中未找到'柜式要求'行")
                return
            
            # 获取C列同一行的数据
            cabinet_value = self._get_cabinet_value(cabinet_sheet, cabinet_row)
            if cabinet_value:
                self.cabinet_type = cabinet_value
                # 检查是否包含"旋转柜"字样
                if "旋转柜" in cabinet_value:
                    self.is_rotating_cabinet = True
                    # print(f"✓ 识别到旋转柜要求: {cabinet_value}")
                else:
                    self.is_rotating_cabinet = False
                    # print(f"✓ 识别到柜式要求: {cabinet_value}")
            else:
                logger.warning("柜式要求的C列数据为空")
                
        except Exception as e:
            logger.error(f"处理柜式要求时出错: {str(e)}")

    def _find_cabinet_sheet(self, workbook_data):
        """查找选型要求及组屏方案工作表"""
        # 可能的工作表名称
        possible_names = ["选型要求及组屏方案", "选型要求", "组屏方案", "要求方案"]
        
        for sheet_name, sheet_data in workbook_data.items():
            # 精确匹配或部分匹配
            if any(name in sheet_name for name in possible_names):
                logger.info(f"找到选型要求及组屏方案工作表: {sheet_name}")
                return sheet_data
        
        logger.warning("未找到选型要求及组屏方案工作表")
        return None

    def _get_cabinet_value(self, sheet_data, target_row):
        """获取柜式要求C列的值"""
        try:
            # 直接获取C列（索引2）的值
            row_data = sheet_data.iloc[target_row, :].astype(str)
            
            if len(row_data) > 2:
                cabinet_value = str(row_data.iloc[2]).strip()
                if cabinet_value and cabinet_value.lower() not in ['nan', 'none', '']:
                    return cabinet_value
            
            return None
        except Exception as e:
            logger.error(f"获取柜式要求值时出错: {e}")
            return None

    def _process_small_busbar_bracket(self, material_sheet):
        """处理小母线支架信息"""
        try:
            log_process_step(logger, "开始处理小母线支架信息")

            # 在A列查找"小母线支架"
            bracket_row = self._find_row_by_keyword(material_sheet, "小母线支架")
            if bracket_row is None:
                log_process_step(logger, "未找到小母线支架行，跳过处理")
                return

            # 获取B列"布局方式"右侧单元格数据（C列）
            layout_value = self._get_spec_value(material_sheet, bracket_row, "布局方式")
            if not layout_value:
                log_process_step(logger, "小母线支架布局方式数据为空")
                return

            # 检查是否为"不安装"
            if "不安装" in layout_value:
                log_process_step(logger, f"小母线支架布局方式为'{layout_value}'，跳过处理")
                return

            self.small_busbar_bracket_layout = layout_value

            # 解析层数信息
            layers = self._parse_bracket_layers(layout_value)

            # 获取下一行的"数量"数据
            quantity_value = self._get_next_row_quantity(material_sheet, bracket_row)
            if quantity_value:
                self.small_busbar_bracket_quantity = quantity_value

            self.small_busbar_bracket = {
                'layout': self.small_busbar_bracket_layout,
                'quantity': self.small_busbar_bracket_quantity,
                'layers': layers  # 新增层数信息
            }

            log_data_info(logger, "小母线支架", details=f"布局方式: {layout_value}, 数量: {quantity_value}, 层数: {layers}")

        except Exception as e:
            log_function_error(logger, "_process_small_busbar_bracket", e)

    def _parse_bracket_layers(self, layout_value):
        """解析小母线支架的层数信息

        Args:
            layout_value (str): 布局方式字符串

        Returns:
            int: 层数，默认为1
        """
        try:
            import re

            # 中文数字到阿拉伯数字的映射
            chinese_to_arabic = {
                '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
                '六': 6, '七': 7, '八': 8, '九': 9, '十': 10
            }

            # 查找"X层"的模式
            pattern = r'([一二三四五六七八九十])层'
            match = re.search(pattern, str(layout_value))

            if match:
                chinese_num = match.group(1)
                layers = chinese_to_arabic.get(chinese_num, 1)
                log_process_step(logger, f"解析到层数: {chinese_num}层 -> {layers}层")
                return layers
            else:
                log_process_step(logger, f"未在布局方式'{layout_value}'中找到层数标注，默认为1层")
                return 1

        except Exception as e:
            log_function_error(logger, "_parse_bracket_layers", e)
            return 1

    def _process_small_busbar_copper(self, material_sheet):
        """处理小母线铜棒信息"""
        try:
            log_process_step(logger, "开始处理小母线铜棒信息")
            
            # 在A列查找"小母线铜棒"
            copper_row = self._find_row_by_keyword(material_sheet, "小母线铜棒")
            if copper_row is None:
                log_process_step(logger, "未找到小母线铜棒行，跳过处理")
                return
            
            # 获取B列"规格"右侧单元格数据（C列）
            spec_value = self._get_spec_value(material_sheet, copper_row, "规格")
            if not spec_value:
                log_process_step(logger, "小母线铜棒规格数据为空")
                return
            
            # 检查是否为"不提供"
            if "不提供" in spec_value:
                log_process_step(logger, f"小母线铜棒规格为'{spec_value}'，跳过处理")
                return
            
            self.small_busbar_copper_spec = spec_value
            
            # 获取下一行的"数量"数据
            quantity_value = self._get_next_row_quantity(material_sheet, copper_row)
            if quantity_value:
                self.small_busbar_copper_quantity = quantity_value
                
            self.small_busbar_copper = {
                'spec': self.small_busbar_copper_spec,
                'quantity': self.small_busbar_copper_quantity
            }
            
            log_data_info(logger, "小母线铜棒", details=f"规格: {spec_value}, 数量: {quantity_value}")
                
        except Exception as e:
            log_function_error(logger, "_process_small_busbar_copper", e)

    def _get_next_row_quantity(self, sheet_data, current_row):
        """获取下一行的数量数据"""
        try:
            # 检查下一行是否存在
            if current_row + 1 >= len(sheet_data):
                return None

            next_row_data = sheet_data.iloc[current_row + 1, :].astype(str)

            # 查找包含"数量"的单元格，然后获取其右侧的值
            for col_idx, cell_value in enumerate(next_row_data):
                cell_str = str(cell_value).strip()
                if "数量" in cell_str and cell_str != 'nan':
                    # 获取右侧单元格的值
                    if col_idx + 1 < len(next_row_data):
                        quantity_value = str(next_row_data.iloc[col_idx + 1]).strip()
                        if quantity_value and quantity_value.lower() not in ['nan', 'none', '']:
                            return quantity_value

            return None
        except Exception as e:
            logger.error(f"获取数量数据时出错: {e}")
            return None

    def _process_purchase_category(self, workbook_data):
        """处理外购类别信息"""
        try:
            log_process_step(logger, "开始处理外购类别信息")

            # 查找"选型要求及组屏方案"工作表
            cabinet_sheet = self._find_cabinet_sheet(workbook_data)
            if cabinet_sheet is None:
                log_process_step(logger, "未找到'选型要求及组屏方案'工作表，跳过外购类别处理")
                return

            # 查找"外购类别"标题行
            purchase_category_row = self._find_purchase_category_title(cabinet_sheet)
            if purchase_category_row is None:
                log_process_step(logger, "未找到'外购类别'标题行，跳过处理")
                return

            # 查找"2.2 分散"的结束行
            end_row = self._find_end_row(cabinet_sheet)
            if end_row is None:
                log_process_step(logger, "未找到'2.2 分散'结束标记，使用工作表末尾")
                end_row = len(cabinet_sheet)

            # 读取H列的外购类别数据
            categories = self._extract_purchase_categories(cabinet_sheet, purchase_category_row + 1, end_row)

            if categories:
                self.purchase_categories = categories
                unique_categories = list(set(categories))
                self.purchase_category_data = {
                    'start_row': purchase_category_row + 1,
                    'end_row': end_row,
                    'categories': categories,
                    'unique_categories': unique_categories
                }

                log_data_info(logger, "外购类别",
                            details=f"共{len(categories)}条记录，{len(unique_categories)}种类别: {', '.join(unique_categories)}")
            else:
                log_process_step(logger, "未找到有效的外购类别数据")

        except Exception as e:
            log_function_error(logger, "_process_purchase_category", e)

    def _find_purchase_category_title(self, sheet_data):
        """查找外购类别标题行"""
        try:
            # 在H列（索引7）查找"外购类别"
            if sheet_data.shape[1] <= 7:
                return None

            h_column = sheet_data.iloc[:, 7].astype(str)

            for idx, cell_value in enumerate(h_column):
                cell_str = str(cell_value).strip()
                if "外购类别" in cell_str and cell_str != 'nan':
                    log_process_step(logger, f"找到'外购类别'标题在第{idx + 1}行")
                    return idx

            return None
        except Exception as e:
            logger.error(f"查找外购类别标题时出错: {e}")
            return None

    def _find_end_row(self, sheet_data):
        """查找"2.2 分散"结束行"""
        try:
            # 在A列查找"2.2 分散"
            a_column = sheet_data.iloc[:, 0].astype(str)

            for idx, cell_value in enumerate(a_column):
                cell_str = str(cell_value).strip()
                if "2.2" in cell_str and "分散" in cell_str and cell_str != 'nan':
                    log_process_step(logger, f"找到'2.2 分散'结束标记在第{idx + 1}行")
                    return idx

            return None
        except Exception as e:
            logger.error(f"查找结束行时出错: {e}")
            return None

    def _extract_purchase_categories(self, sheet_data, start_row, end_row):
        """提取外购类别数据"""
        try:
            categories = []

            # 确保H列存在
            if sheet_data.shape[1] <= 7:
                return categories

            # 提取指定范围内H列的数据
            h_column_range = sheet_data.iloc[start_row:end_row, 7].astype(str)

            for idx, cell_value in enumerate(h_column_range):
                cell_str = str(cell_value).strip()
                # 过滤掉空值和无效数据
                if cell_str and cell_str.lower() not in ['nan', 'none', ''] and len(cell_str) > 1:
                    categories.append(cell_str)
                    log_process_step(logger, f"提取外购类别数据第{start_row + idx + 1}行: {cell_str}")

            return categories
        except Exception as e:
            logger.error(f"提取外购类别数据时出错: {e}")
            return []

    def _process_reserved_opening_devices(self, workbook_data):
        """处理预留开孔装置信息"""
        try:
            log_process_step(logger, "开始处理预留开孔装置信息")

            # 查找"选型要求及组屏方案"工作表
            cabinet_sheet = self._find_cabinet_sheet(workbook_data)
            if cabinet_sheet is None:
                log_process_step(logger, "未找到'选型要求及组屏方案'工作表，跳过预留开孔装置处理")
                return

            # 查找"外购类别"标题行
            purchase_category_row = self._find_purchase_category_title(cabinet_sheet)
            if purchase_category_row is None:
                log_process_step(logger, "未找到'外购类别'标题行，跳过处理")
                return

            # 查找"2.2 分散"的结束行
            end_row = self._find_end_row(cabinet_sheet)
            if end_row is None:
                log_process_step(logger, "未找到'2.2 分散'结束标记，使用工作表末尾")
                end_row = len(cabinet_sheet)

            # 提取预留开孔装置数据
            devices = self._extract_reserved_opening_devices(cabinet_sheet, purchase_category_row + 1, end_row)

            if devices:
                self.reserved_opening_devices = devices
                self.reserved_opening_data = {
                    'start_row': purchase_category_row + 1,
                    'end_row': end_row,
                    'devices': devices,
                    'device_count': len(devices)
                }

                log_data_info(logger, "预留开孔装置",
                            details=f"共{len(devices)}台设备")
            else:
                log_process_step(logger, "未找到预留开孔装置数据")

        except Exception as e:
            log_function_error(logger, "_process_reserved_opening_devices", e)

    def _extract_reserved_opening_devices(self, sheet_data, start_row, end_row):
        """提取预留开孔装置数据"""
        try:
            devices = []

            # 确保所需列都存在 (B列索引1, D列索引3, G列索引6, H列索引7)
            if sheet_data.shape[1] <= 7:
                return devices

            # 遍历指定范围内的数据
            for row_idx in range(start_row, end_row):
                try:
                    # 获取H列的外购类别
                    h_value = str(sheet_data.iloc[row_idx, 7]).strip()

                    # 只处理"预留开孔及配线"的记录
                    if "预留开孔及配线" in h_value:
                        # 获取B列屏柜编号
                        b_value = str(sheet_data.iloc[row_idx, 1]).strip()
                        # 获取D列设备名称
                        d_value = str(sheet_data.iloc[row_idx, 3]).strip()
                        # 获取G列数量
                        g_value = str(sheet_data.iloc[row_idx, 6]).strip()

                        # 过滤有效数据
                        if (b_value and b_value not in ['nan', ''] and
                            d_value and d_value not in ['nan', ''] and
                            g_value and g_value not in ['nan', '']):

                            device = {
                                'row': row_idx + 1,
                                'cabinet_no': b_value,
                                'device_name': d_value,
                                'quantity': g_value
                            }
                            devices.append(device)

                            log_process_step(logger, f"提取预留开孔装置第{row_idx + 1}行: 屏柜编号={b_value}, 设备名称={d_value}, 数量={g_value}")

                except Exception as row_e:
                    logger.warning(f"处理第{row_idx + 1}行数据时出错: {row_e}")
                    continue

            return devices
        except Exception as e:
            logger.error(f"提取预留开孔装置数据时出错: {e}")
            return []


def process_design_document_auto(design_doc_path, gui_callback=None):
    """
    自动处理设计说明书的主函数

    Args:
        design_doc_path (str): 设计说明书文件路径
        gui_callback (function): GUI回调函数，用于切换界面

    Returns:
        tuple: (material_spec, cable_type, is_rotating_cabinet, small_busbar_data, purchase_category_data, reserved_opening_data, cabinet_type, success)
    """
    processor = DesignDocProcessor()
    material_spec, cable_type, is_rotating_cabinet, small_busbar_data, purchase_category_data, reserved_opening_data, success, error_msg = processor.process_design_document(design_doc_path)

    if not success:
        # 显示错误信息
        messagebox.showwarning(
            "自动识别失败",
            f"无法从设计说明书中自动识别参数：\n\n{error_msg}\n\n"
            "将自动切换到手动选择模式，请手动设置线材规格和线径选型。"
        )

        # 调用GUI回调函数切换到手动选择界面
        if gui_callback:
            gui_callback("手动选择")

        return None, None, False, None, None, None, None, False

    return material_spec, cable_type, is_rotating_cabinet, small_busbar_data, purchase_category_data, reserved_opening_data, processor.cabinet_type, True


if __name__ == "__main__":
    # 测试代码
    # print("设计说明书处理模块测试")
    # print("=" * 50)
    
    # 创建处理器实例
    processor = DesignDocProcessor()
    
    # 测试文件路径（这里使用示例路径）
    test_file = "input/设计说明书示例.xlsx"
    
    # print(f"测试文件: {test_file}")
    
    # 处理设计说明书
    material_spec, cable_type, is_rotating_cabinet, small_busbar_data, purchase_category_data, reserved_opening_data, success, error_msg = processor.process_design_document(test_file)
    
    # print(f"处理结果:")
    # print(f"  线材规格: {material_spec}")
    # print(f"  线径选型: {cable_type}")
    # print(f"  是否旋转柜: {is_rotating_cabinet}")
    # print(f"  小母线支架: {small_busbar_data['bracket'] if small_busbar_data else None}")
    # print(f"  小母线铜棒: {small_busbar_data['copper'] if small_busbar_data else None}")
    # print(f"  柜式要求: {processor.cabinet_type}")
    # print(f"  是否成功: {success}")
    # if not success:
    #     print(f"  错误信息: {error_msg}")
    
    # print("=" * 50) 
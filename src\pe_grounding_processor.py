# pe_grounding_processor.py
# 装置PE接地处理器

import pandas as pd
import re
import logging
from logger_config import get_main_logger, log_function_start, log_function_end, log_function_error, log_process_step, log_data_info

# 获取日志记录器
logger = get_main_logger()
logger.setLevel(logging.INFO)  # 设置为INFO级别以便看到调试信息


def extract_pe_grounding_data(wiring_df):
    """
    从屏柜配线套管表中提取装置PE接地数据
    
    Args:
        wiring_df: 预处理后的屏柜配线套管表DataFrame
        
    Returns:
        tuple: (pe_grounding_df, filtered_wiring_df) - PE接地数据和过滤后的原始数据
    """
    try:
        log_function_start(logger, "extract_pe_grounding_data")
        log_data_info(logger, "输入数据", len(wiring_df))
        
        # 创建PE接地数据列表
        pe_grounding_records = []
        # 记录需要从原始数据中移除的索引
        indices_to_remove = []
        
        # 获取列名
        device_type_col = None
        wire_start_col = None
        wire_end_col = None
        cabinet_col = None
        color_col = None
        
        for col in wiring_df.columns:
            if "设备类型" in col and ("起点" in col or "终点" in col):
                device_type_col = col
            elif "导线起点" in col:
                wire_start_col = col
            elif "导线终点" in col:
                wire_end_col = col
            elif "屏柜编号" in col:
                cabinet_col = col
            elif "颜色" in col and "线径" in col:
                color_col = col
        
        if not all([device_type_col, wire_start_col, cabinet_col]):
            raise ValueError("缺少必要的列：设备类型、导线起点或屏柜编号")
        
        log_process_step(logger, "列名识别完成", f"设备类型列={device_type_col}, 导线起点列={wire_start_col}")
        
        # 按屏柜编号分组处理
        for cabinet_num, group in wiring_df.groupby(cabinet_col):
            log_process_step(logger, f"处理屏柜 {cabinet_num}")
            
            # 在设备类型中找到"装置"
            device_mask = group[device_type_col].astype(str).str.contains('装置', na=False)
            device_records = group[device_mask]
            
            if device_records.empty:
                continue
                
            log_process_step(logger, f"屏柜 {cabinet_num} 找到装置记录", len(device_records))
            
            # 处理每条装置记录
            for idx, row in device_records.iterrows():
                wire_start = str(row[wire_start_col]) if pd.notna(row[wire_start_col]) else ""
                wire_end = str(row[wire_end_col]) if wire_end_col and pd.notna(row[wire_end_col]) else ""
                
                # 解析导线起点，以"/"为分界点分割
                if "/" in wire_start:
                    start_parts = wire_start.split("/")
                    actual_start = start_parts[0].strip()
                    actual_end = start_parts[1].strip() if len(start_parts) > 1 else ""
                else:
                    actual_start = wire_start.strip()
                    actual_end = wire_end.strip()
                
                # 检查是否满足PE接地条件
                if is_pe_grounding_wire(actual_start, actual_end):
                    # 创建PE接地记录
                    pe_record = {
                        '屏柜编号': row[cabinet_col],
                        '设备类型（起点/终点）': row[device_type_col],  # 只保留这一列
                        '导线起点': actual_start,
                        '导线终点': actual_end,
                        '颜色/线径标识': row[color_col] if color_col else "",
                        '导线根数': 1,  # PE接地导线每条记录为1根
                        '对应线径': "",  # 后续填充
                        '长度': "",     # 后续填充
                        '对应编码': ""  # 后续填充
                    }
                    
                    pe_grounding_records.append(pe_record)
                    indices_to_remove.append(idx)
                    
                    log_process_step(logger, f"识别PE接地导线", f"起点={actual_start}, 终点={actual_end}")
        
        # 创建PE接地DataFrame
        pe_grounding_df = pd.DataFrame(pe_grounding_records)
        
        # 从原始数据中移除PE接地导线
        filtered_wiring_df = wiring_df.drop(indices_to_remove).copy()
        
        log_data_info(logger, "PE接地数据", len(pe_grounding_df))
        log_data_info(logger, "过滤后原始数据", len(filtered_wiring_df))
        log_function_end(logger, "extract_pe_grounding_data")
        
        return pe_grounding_df, filtered_wiring_df
        
    except Exception as e:
        log_function_error(logger, "extract_pe_grounding_data", str(e))
        return pd.DataFrame(), wiring_df


def is_pe_grounding_wire(start_point, end_point):
    """
    判断是否为PE接地导线
    
    条件：
    1. 导线起点或终点含有GND
    2. 导线起点或终点含有"n"的元器件标号，且该标号对应的端号为PE
    
    Args:
        start_point: 导线起点
        end_point: 导线终点
        
    Returns:
        bool: 是否为PE接地导线
    """
    try:
        # 检查是否含有GND
        if "GND" in start_point.upper() or "GND" in end_point.upper():
            return True
        
        # 检查是否含有"n"的元器件标号且端号为PE
        # 格式如：1-71n:PE、1n-1:PE
        pe_pattern = r'.*\d+.*n.*:PE'
        
        if re.search(pe_pattern, start_point, re.IGNORECASE) or re.search(pe_pattern, end_point, re.IGNORECASE):
            return True
            
        return False
        
    except Exception as e:
        logger.warning(f"判断PE接地导线时出错: {e}")
        return False


def process_pe_grounding_wires(pe_grounding_df, wire_spec_def, wire_length_def, terminal_def, sleeve_def, material_type, cable_type, project_type, is_rotating_cabinet=False):
    """
    处理PE接地导线数据，添加线径、长度、编码信息，并进行套管匹配

    Args:
        pe_grounding_df: PE接地数据DataFrame
        wire_spec_def: 线材规格定义文件路径
        wire_length_def: 线长定义文件路径
        terminal_def: 压头匹配文件路径
        sleeve_def: 套管匹配文件路径
        material_type: 材料类型
        cable_type: 线缆类型
        project_type: 项目类型
        is_rotating_cabinet: 是否为旋转柜

    Returns:
        tuple: (processed_pe_df, terminal_counts_df, sleeve_counts_df) - 处理后的PE接地数据、压头匹配结果和套管匹配结果
    """
    try:
        log_function_start(logger, "process_pe_grounding_wires")
        
        if pe_grounding_df.empty:
            log_process_step(logger, "无PE接地数据需要处理")
            return pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), []

        # 复制数据避免修改原始数据
        processed_df = pe_grounding_df.copy()

        # 1. 添加线径信息
        log_process_step(logger, "添加线径信息")
        processed_df = add_pe_wire_diameter(processed_df, wire_spec_def, material_type, cable_type)

        # 2. 添加长度信息
        log_process_step(logger, "添加长度信息")
        processed_df = add_pe_wire_length(processed_df, wire_length_def, is_rotating_cabinet)

        # 3. 添加编码信息
        log_process_step(logger, "添加编码信息")
        processed_df = add_pe_wire_code(processed_df, wire_spec_def, material_type, cable_type)

        # 4. 处理压头匹配
        log_process_step(logger, "处理压头匹配")
        terminal_counts_df = match_pe_terminals(processed_df, terminal_def)

        # 5. 处理套管匹配
        log_process_step(logger, "处理套管匹配")
        sleeve_counts_df, sleeve_residual_list = match_pe_sleeves(processed_df, sleeve_def, project_type)

        log_function_end(logger, "process_pe_grounding_wires")
        return processed_df, terminal_counts_df, sleeve_counts_df, sleeve_residual_list
        
    except Exception as e:
        log_function_error(logger, "process_pe_grounding_wires", str(e))
        return pe_grounding_df, pd.DataFrame(), pd.DataFrame(), []


def add_pe_wire_diameter(pe_df, wire_spec_def, material_type, cable_type):
    """
    为PE接地导线添加线径信息（复用现有规则）
    """
    try:
        from wire_diameter_matcher import add_wire_diameter
        return add_wire_diameter(pe_df, wire_spec_def, material_type, cable_type)
    except Exception as e:
        logger.error(f"添加PE导线线径失败: {e}")
        return pe_df


def add_pe_wire_length(pe_df, wire_length_def, is_rotating_cabinet=False):
    """
    为PE接地导线添加长度信息（复用现有规则）
    """
    try:
        from wire_length_processor import add_wire_length
        
        # 添加单根长度
        pe_df = add_wire_length(pe_df, wire_length_def)
        
        # 计算总长度（考虑旋转柜系数）
        if '单根长度' in pe_df.columns:
            rotation_factor = 2 if is_rotating_cabinet else 1
            pe_df['长度'] = pe_df['单根长度'] * rotation_factor
        
        return pe_df
    except Exception as e:
        logger.error(f"添加PE导线长度失败: {e}")
        return pe_df


def add_pe_wire_code(pe_df, wire_spec_def, material_type, cable_type):
    """
    为PE接地导线添加编码信息（复用现有规则）
    """
    try:
        from wire_code_matcher import add_wire_code
        processed_df, _ = add_wire_code(pe_df, wire_spec_def, material_type, cable_type)
        return processed_df
    except Exception as e:
        logger.error(f"添加PE导线编码失败: {e}")
        return pe_df


def match_pe_terminals(pe_df, terminal_def):
    """
    PE接地导线压头匹配 - 特殊规则
    GND一端使用M6，另一端使用M4，并线要求为单，概率100%
    """
    try:
        log_function_start(logger, "match_pe_terminals")

        if pe_df.empty:
            return pd.DataFrame()

        # 读取压头匹配表
        sheet2 = pd.read_excel(terminal_def, sheet_name=1)

        # 调试：打印压头匹配表的结构
        logger.info(f"压头匹配表列名: {sheet2.columns.tolist()}")
        logger.info(f"压头匹配表接口类型: {sheet2['接口类型'].unique()}")
        logger.info(f"压头匹配表对应线径: {sheet2['对应线径'].unique()}")

        # 查找编码列名
        code_col = None
        star_code_col = None

        for col in sheet2.columns:
            if '编码' in col and ('星瀚' in col or '瀚' in col):
                code_col = col
            elif '编码' in col and ('星空' in col or '空' in col):
                star_code_col = col

        if not code_col:
            # 如果没找到星瀚编码列，尝试其他可能的列名
            for col in sheet2.columns:
                if '编码' in col and not star_code_col:
                    code_col = col
                    break

        logger.info(f"使用星瀚编码列: {code_col}")
        logger.info(f"使用星空编码列: {star_code_col}")

        # 打印前几行数据以便调试
        logger.info("压头匹配表前5行数据:")
        for i, row in sheet2.head().iterrows():
            logger.info(f"  行{i}: 接口类型={row.get('接口类型', 'N/A')}, 对应线径={row.get('对应线径', 'N/A')}, 编码={row.get(code_col, 'N/A') if code_col else 'N/A'}")

        terminal_records = []

        for idx, row in pe_df.iterrows():
            wire_diameter = row.get('对应线径', '')
            cabinet_num = row.get('屏柜编号', '')
            start_point = row.get('导线起点', '')
            end_point = row.get('导线终点', '')

            logger.info(f"处理PE接地导线: 屏柜={cabinet_num}, 线径={wire_diameter}, 起点={start_point}, 终点={end_point}")

            # 判断哪一端是GND，哪一端是PE
            # 根据需求，GND端使用M6，PE端使用M4，并线要求为单

            gnd_interface = "M6"  # GND端使用M6
            pe_interface = "M4"   # PE端使用M4
            parallel_requirement = "单"  # 并线要求为单

            # 为每一端匹配压头
            interfaces = [
                (gnd_interface, "GND端"),
                (pe_interface, "PE端")
            ]

            for interface_type, end_type in interfaces:
                # 查找匹配的压头 - 通过接口类型、对应线径、并线要求三个条件匹配

                # 检查是否有并线要求列
                parallel_col = None
                for col in sheet2.columns:
                    if '并线' in col and '要求' in col:
                        parallel_col = col
                        break

                # 构建匹配条件
                base_condition = sheet2['接口类型'] == interface_type

                # 添加线径匹配条件
                if wire_diameter:
                    # 先尝试精确匹配
                    wire_condition = sheet2['对应线径'].astype(str) == str(wire_diameter)

                    # 如果精确匹配没有结果，尝试数值匹配
                    if not sheet2[base_condition & wire_condition].empty:
                        base_condition = base_condition & wire_condition
                    else:
                        try:
                            wire_diameter_float = float(wire_diameter)
                            def safe_float_compare(x):
                                try:
                                    return float(x) == wire_diameter_float
                                except (ValueError, TypeError):
                                    return False
                            wire_condition = sheet2['对应线径'].apply(safe_float_compare)
                            base_condition = base_condition & wire_condition
                        except (ValueError, TypeError):
                            pass

                # 添加并线要求条件
                if parallel_col:
                    parallel_condition = sheet2[parallel_col] == parallel_requirement
                    base_condition = base_condition & parallel_condition

                # 应用所有条件
                matching_terminals = sheet2[base_condition]

                logger.info(f"查找{end_type}压头: 接口类型={interface_type}, 线径={wire_diameter}, 并线要求={parallel_requirement}, 找到{len(matching_terminals)}个匹配")

                # 如果还是没找到，显示可用的选项
                if matching_terminals.empty:
                    interface_data = sheet2[sheet2['接口类型'] == interface_type]
                    if interface_data.empty:
                        logger.warning(f"接口类型{interface_type}在压头匹配表中不存在")
                        # 显示所有可用的接口类型
                        all_interfaces = sheet2['接口类型'].unique()
                        logger.warning(f"所有可用的接口类型: {all_interfaces}")
                    else:
                        available_options = interface_data['对应线径'].unique()
                        logger.warning(f"接口类型{interface_type}可用的线径选项: {available_options}")

                if not matching_terminals.empty:
                    terminal_info = matching_terminals.iloc[0]

                    # 动态获取编码列的值
                    code_value = terminal_info.get(code_col, '') if code_col else ''
                    star_code_value = terminal_info.get(star_code_col, '') if star_code_col else ''

                    logger.info(f"匹配到压头: 星瀚编码={code_value}, 星空编码={star_code_value}, 名称={terminal_info.get('压头名称', '')}")

                    terminal_record = {
                        '屏柜编号': cabinet_num,
                        '压头星瀚编码': code_value,
                        '压头星空编码': star_code_value,
                        '压头名称': terminal_info.get('压头名称', ''),
                        '实际数量': 1,  # PE接地导线每端一个压头
                        '概率': 1.0     # PE接地压头概率为100%
                    }

                    terminal_records.append(terminal_record)
                    logger.info(f"添加{end_type}压头记录: {terminal_info.get('压头名称', '')}")
                else:
                    logger.warning(f"未找到{end_type}压头匹配: 接口类型={interface_type}, 线径={wire_diameter}")

        # 创建DataFrame并按照与主压头匹配相同的逻辑处理
        if terminal_records:
            terminal_df = pd.DataFrame(terminal_records)

            # 步骤1：按屏柜编号、压头星瀚编码、压头星空编码、压头名称、概率分组，合并实际数量
            grouped_counts = terminal_df.groupby(
                ['屏柜编号', '压头星瀚编码', '压头星空编码', '压头名称', '概率'], as_index=False
            ).agg({
                '实际数量': 'sum'  # 合并相同概率的实际数量
            })

            # 步骤2：对每组数据，将合并后的实际数量乘以概率
            final_records = []
            for idx, row in grouped_counts.iterrows():
                # 计算最终数量：实际数量 × 概率
                actual_quantity = row['实际数量']
                probability = row['概率']
                calculated_quantity = actual_quantity * probability

                final_records.append({
                    '屏柜编号': row['屏柜编号'],
                    '压头星瀚编码': row['压头星瀚编码'],
                    '压头星空编码': row['压头星空编码'],
                    '压头名称': row['压头名称'],
                    '数量': calculated_quantity
                })

            terminal_df = pd.DataFrame(final_records)

            # 重新排列列的顺序
            terminal_df = terminal_df[['屏柜编号', '压头星瀚编码', '压头星空编码', '压头名称', '数量']]
        else:
            terminal_df = pd.DataFrame(columns=['屏柜编号', '压头星瀚编码', '压头星空编码', '压头名称', '数量'])

        log_data_info(logger, "PE接地压头匹配结果", len(terminal_df))
        log_function_end(logger, "match_pe_terminals")

        return terminal_df

    except Exception as e:
        log_function_error(logger, "match_pe_terminals", str(e))
        return pd.DataFrame()


def match_pe_sleeves(pe_df, sleeve_def, project_type):
    """
    PE接地导线套管匹配
    专门为PE接地导线实现的套管匹配逻辑，设置正确的数据来源
    """
    try:
        log_function_start(logger, "match_pe_sleeves")

        if pe_df.empty:
            return pd.DataFrame()

        # 读取套管匹配表
        sleeve_spec_df = pd.read_excel(sleeve_def, sheet_name=0)  # 明确读取Sheet1

        logger.info(f"套管匹配表列名: {sleeve_spec_df.columns.tolist()}")
        logger.info(f"套管匹配表前3行数据:")
        for i, row in sleeve_spec_df.head(3).iterrows():
            logger.info(f"  行{i}: {dict(row)}")

        # 检查是否有工程要求列
        if '工程要求' in sleeve_spec_df.columns:
            # 根据项目类型筛选套管规格
            if project_type == "福建":
                project_sleeve = sleeve_spec_df[sleeve_spec_df['工程要求'] == '福建']
            else:
                project_sleeve = sleeve_spec_df[sleeve_spec_df['工程要求'] != '福建']
        else:
            # 如果没有工程要求列，使用所有数据
            logger.warning("套管匹配表中没有'工程要求'列，使用所有套管规格")
            project_sleeve = sleeve_spec_df

        if project_sleeve.empty:
            logger.warning(f"未找到项目类型 {project_type} 的套管规格")
            return pd.DataFrame()

        # 创建损耗率和最小有效值映射
        sleeve_loss_rate_map = dict(zip(project_sleeve['套管星瀚编码'], project_sleeve['损耗率']))
        sleeve_min_valid_map = dict(zip(project_sleeve['套管星瀚编码'], project_sleeve['最小有效值']))
        # 注意：套管匹配表中没有"最小有效值小数位数"列，使用默认值0
        sleeve_min_valid_decimal_map = {code: 0 for code in project_sleeve['套管星瀚编码']}

        sleeve_records = []

        # 处理PE接地数据
        for idx, row in pe_df.iterrows():
            cabinet = row['屏柜编号']
            diameter = row['对应线径']
            count = row['导线根数']
            device_type = row.get('设备类型（起点/终点）', '')

            sleeve_type = "单"  # PE接地导线套接根数为"单"

            logger.info(f"处理PE接地导线套管: 屏柜={cabinet}, 线径={diameter}, 设备类型={device_type}")

            # 根据设备类型判断接口要求
            # 解析设备类型，格式通常为"起点设备/终点设备"
            if "/" in device_type:
                start_device, end_device = device_type.split("/", 1)
                start_device = start_device.strip()
                end_device = end_device.strip()
            else:
                # 如果没有"/"分隔符，则起点和终点都是同一个设备类型
                start_device = device_type.strip()
                end_device = device_type.strip()

            # 判断接口要求
            start_interface = "接地铜排" if "接地铜排" in start_device else "非接地铜排"
            end_interface = "接地铜排" if "接地铜排" in end_device else "非接地铜排"

            logger.info(f"设备类型解析: 起点设备={start_device}, 终点设备={end_device}")
            logger.info(f"接口要求判断: 起点={start_interface}, 终点={end_interface}")

            # 匹配起点端套管
            start_match = project_sleeve[
                (project_sleeve['对应线径'] == diameter) &
                (project_sleeve['接口要求'] == start_interface) &
                (project_sleeve['套接根数'] == sleeve_type)
            ]

            if not start_match.empty:
                sleeve = start_match.iloc[0]
                code = sleeve['套管星瀚编码']

                sleeve_record = {
                    '屏柜编号': cabinet,
                    '套管名称': sleeve['套管名称'],
                    '套管型号': sleeve['套管型号'],
                    '套管星瀚编码': code,
                    '套管星空编码': sleeve['套管星空编码'],
                    '单端长度': sleeve['单端长度/米'],
                    '系统单位长度': sleeve['系统单位长度/米'],
                    '色带型号': sleeve['色带型号'],
                    '数量': count,
                    '来源': '装置PE接地(起点-单)',  # 设置正确的数据来源
                    '损耗率': sleeve_loss_rate_map.get(code, 0),
                    '最小有效值': sleeve_min_valid_map.get(code, 0),
                    '最小有效值小数位数': sleeve_min_valid_decimal_map.get(code, 0)
                }

                sleeve_records.append(sleeve_record)
                logger.info(f"匹配到起点端套管: {sleeve['套管名称']}")

            # 匹配终点端套管
            end_match = project_sleeve[
                (project_sleeve['对应线径'] == diameter) &
                (project_sleeve['接口要求'] == end_interface) &
                (project_sleeve['套接根数'] == sleeve_type)
            ]

            if not end_match.empty:
                sleeve = end_match.iloc[0]
                code = sleeve['套管星瀚编码']

                sleeve_record = {
                    '屏柜编号': cabinet,
                    '套管名称': sleeve['套管名称'],
                    '套管型号': sleeve['套管型号'],
                    '套管星瀚编码': code,
                    '套管星空编码': sleeve['套管星空编码'],
                    '单端长度': sleeve['单端长度/米'],
                    '系统单位长度': sleeve['系统单位长度/米'],
                    '色带型号': sleeve['色带型号'],
                    '数量': count,
                    '来源': '装置PE接地(终点-单)',  # 设置正确的数据来源
                    '损耗率': sleeve_loss_rate_map.get(code, 0),
                    '最小有效值': sleeve_min_valid_map.get(code, 0),
                    '最小有效值小数位数': sleeve_min_valid_decimal_map.get(code, 0)
                }

                sleeve_records.append(sleeve_record)
                logger.info(f"匹配到终点端套管: {sleeve['套管名称']}")

        # 创建DataFrame和计算残值
        residual_list = []

        if sleeve_records:
            sleeve_df = pd.DataFrame(sleeve_records)

            # 计算套管总长度
            sleeve_df['套管总长度'] = sleeve_df['单端长度'] * sleeve_df['数量']

            # 按照屏柜编号和套管编码分组合并
            group_cols = ['屏柜编号', '套管名称', '套管型号', '套管星瀚编码', '套管星空编码',
                         '单端长度', '系统单位长度', '色带型号', '来源', '损耗率', '最小有效值', '最小有效值小数位数']

            final_df = sleeve_df.groupby(group_cols, dropna=False).agg({
                '套管总长度': 'sum',
                '数量': 'sum'
            }).reset_index()

            final_df = final_df.rename(columns={'数量': '合并数量', '来源': '数据来源'})

            # 计算系统分子和残值
            system_molecule_list = []
            for idx, row in final_df.iterrows():
                total_length = row['套管总长度']
                system_unit_length = row['系统单位长度']
                loss_rate = row['损耗率']
                min_valid_value = row['最小有效值']
                code = row['套管星瀚编码']
                sleeve_name = row['套管名称']

                # 计算系统分子
                if system_unit_length > 0:
                    system_molecule = total_length / system_unit_length
                else:
                    system_molecule = 0

                system_molecule_list.append(system_molecule)

                # 计算残值 - 正确的计算方式
                if system_molecule > 0 and min_valid_value > 0:
                    # 获取最小有效值的小数位数
                    from decimal import Decimal, ROUND_DOWN
                    min_valid_decimal = Decimal(str(min_valid_value))
                    decimal_places = abs(min_valid_decimal.as_tuple().exponent)

                    # 将系统分子保留到最小有效值相同的小数位数，超出部分截断
                    truncated_molecule = float(Decimal(str(system_molecule)).quantize(
                        Decimal('0.' + '0' * decimal_places),
                        rounding=ROUND_DOWN  # 截断，不是四舍五入
                    ))

                    # 计算截断的部分（这就是残值）
                    residual_value = system_molecule - truncated_molecule

                    # 如果残值大于最小有效值，则记录残值
                    if residual_value >= min_valid_value:
                        residual_record = {
                            '物料编码': code,
                            '物料名称': sleeve_name,
                            '残值': residual_value
                        }
                        residual_list.append(residual_record)
                        logger.info(f"PE接地套管残值: {sleeve_name}({code}) = {residual_value:.{decimal_places+2}f} (系统分子截断)")

            final_df['系统分子'] = system_molecule_list

            # 重新排列列的顺序
            final_df = final_df[['屏柜编号', '套管名称', '套管型号', '套管星瀚编码', '套管星空编码',
                               '套管总长度', '系统单位长度', '色带型号', '合并数量', '数据来源',
                               '系统分子', '损耗率', '最小有效值', '最小有效值小数位数']]
        else:
            final_df = pd.DataFrame()

        log_data_info(logger, "PE接地套管匹配结果", len(final_df))
        log_data_info(logger, "PE接地套管残值", len(residual_list))
        log_function_end(logger, "match_pe_sleeves")

        return final_df, residual_list

    except Exception as e:
        log_function_error(logger, "match_pe_sleeves", str(e))
        return pd.DataFrame(), []


def convert_pe_to_wire_statistics(pe_df):
    """
    将PE接地数据转换为导线统计格式，用于长度汇总
    """
    try:
        log_function_start(logger, "convert_pe_to_wire_statistics")

        if pe_df.empty:
            return pd.DataFrame()

        # 转换为导线统计格式
        wire_stats_records = []

        for _, row in pe_df.iterrows():
            # 创建导线统计记录
            wire_stat = {
                '屏柜编号': row.get('屏柜编号', ''),
                '设备类型（起点/终点）': row.get('设备类型（起点/终点）', ''),
                '颜色/线径标识': row.get('颜色/线径标识', ''),
                '对应线径': row.get('对应线径', ''),
                '导线根数': 1,  # PE接地导线每条记录为1根
                '单根长度': row.get('单根长度', 0),
                '长度': row.get('长度', 0),
                '对应编码': row.get('对应编码', ''),
                '备注': 'PE接地导线'
            }

            wire_stats_records.append(wire_stat)

        wire_stats_df = pd.DataFrame(wire_stats_records)

        log_data_info(logger, "PE接地转导线统计", len(wire_stats_df))
        log_function_end(logger, "convert_pe_to_wire_statistics")

        return wire_stats_df

    except Exception as e:
        log_function_error(logger, "convert_pe_to_wire_statistics", str(e))
        return pd.DataFrame()



